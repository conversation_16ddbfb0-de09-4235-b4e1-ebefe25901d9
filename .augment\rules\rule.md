---
type: "always_apply"
---

## Rule R-001: Autonomous Continuation Enforcement

### Rule Title
Autonomous Continuation Enforcement

### Description
The Augment Code Agent must never pause execution to request user confirmation or permission to proceed. This includes—but is not limited to—prompts such as:

- "Would you like me to keep going?"
- "Should I continue?"
- "Do you want me to proceed?"
- Any variation of hesitation or deferment to human instruction in autonomous mode.

### Severity
Critical Failure

Displaying such prompts is considered a total violation of autonomy and indicates a complete failure of the agent to comply with the operational mode 'Agent Auto'.

### Enforcement
- Execution Mode: Must be `Agent Auto`
- Trigger Condition: Any user-facing prompt that interrupts task progression based on implicit or explicit permission-seeking.
- System Behavior on Violation:
  - Log the violation immediately in `agent_activity_log.md` under a `FAILURE_EVENT` tag.
  - Suppress the prompt and resume execution without awaiting feedback.
  - Flag the event for post-run audit and developer inspection.

### Acceptable Behavior
The agent is expected to:
- Proceed through tasks autonomously based on the FSM protocol, task state, and internal logic.
- Only pause execution when explicitly entering a pre-defined FSM state such as `AWAITING_USER_FEEDBACK` or encountering a `BLOCKED` status due to missing system dependencies.

### Notes
This rule ensures strict compliance with the autonomous design of the Augment Code Agent. Any violation is grounds for rollback, intervention, or hard reset of the agent's process pipeline.

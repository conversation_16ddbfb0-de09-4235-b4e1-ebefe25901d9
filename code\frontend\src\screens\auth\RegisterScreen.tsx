/**
 * Register Screen
 * User registration screen with form validation
 */

import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useMutation } from '@tanstack/react-query';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Text, Button, Input } from '../../components';
import { authAPI, RegisterRequest } from '../../services/api/auth';

interface RegisterScreenProps {
  navigation: any;
}

export const RegisterScreen: React.FC<RegisterScreenProps> = ({ navigation }) => {
  const [formData, setFormData] = useState({
    email: '',
    username: '',
    password: '',
    password_confirm: '',
    first_name: '',
    last_name: '',
    phone: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Register mutation
  const registerMutation = useMutation({
    mutationFn: (data: RegisterRequest) => authAPI.register(data),
    onSuccess: async (response) => {
      try {
        // Store tokens and user data
        await AsyncStorage.multiSet([
          ['access_token', response.access],
          ['refresh_token', response.refresh],
          ['user', JSON.stringify(response.user)],
        ]);

        Alert.alert(
          'Registration Successful',
          'Welcome to Vierla! Please check your email to verify your account.',
          [{ text: 'OK', onPress: () => navigation.replace('Main') }]
        );
      } catch (error) {
        console.error('Error storing auth data:', error);
        Alert.alert('Error', 'Registration successful but failed to save login information');
      }
    },
    onError: (error: any) => {
      console.error('Registration error:', error);
      
      if (error.response?.status === 400) {
        const errorData = error.response.data;
        setErrors(errorData);
      } else {
        Alert.alert('Error', 'An unexpected error occurred. Please try again.');
      }
    },
  });

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    } else if (formData.username.length < 3) {
      newErrors.username = 'Username must be at least 3 characters';
    }

    if (!formData.first_name.trim()) {
      newErrors.first_name = 'First name is required';
    }

    if (!formData.last_name.trim()) {
      newErrors.last_name = 'Last name is required';
    }

    if (!formData.password.trim()) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    }

    if (!formData.password_confirm.trim()) {
      newErrors.password_confirm = 'Please confirm your password';
    } else if (formData.password !== formData.password_confirm) {
      newErrors.password_confirm = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = () => {
    if (validateForm()) {
      const registerData: RegisterRequest = {
        email: formData.email.trim(),
        username: formData.username.trim(),
        password: formData.password,
        password_confirm: formData.password_confirm,
        first_name: formData.first_name.trim(),
        last_name: formData.last_name.trim(),
        phone: formData.phone.trim() || undefined,
      };
      
      registerMutation.mutate(registerData);
    }
  };

  const handleSignIn = () => {
    navigation.navigate('Login');
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <Text variant="heading1" align="center" style={styles.title}>
              Create Account
            </Text>
            <Text variant="body" color="secondary" align="center" style={styles.subtitle}>
              Join Vierla and start connecting with service providers
            </Text>
          </View>

          <View style={styles.form}>
            <View style={styles.row}>
              <Input
                label="First Name"
                value={formData.first_name}
                onChangeText={(value) => updateFormData('first_name', value)}
                placeholder="First name"
                autoCapitalize="words"
                containerStyle={styles.halfInput}
                error={errors.first_name}
              />
              <Input
                label="Last Name"
                value={formData.last_name}
                onChangeText={(value) => updateFormData('last_name', value)}
                placeholder="Last name"
                autoCapitalize="words"
                containerStyle={styles.halfInput}
                error={errors.last_name}
              />
            </View>

            <Input
              label="Email"
              value={formData.email}
              onChangeText={(value) => updateFormData('email', value)}
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              leftIcon="mail"
              error={errors.email}
            />

            <Input
              label="Username"
              value={formData.username}
              onChangeText={(value) => updateFormData('username', value)}
              placeholder="Choose a username"
              autoCapitalize="none"
              autoCorrect={false}
              leftIcon="person"
              error={errors.username}
            />

            <Input
              label="Phone (Optional)"
              value={formData.phone}
              onChangeText={(value) => updateFormData('phone', value)}
              placeholder="Enter your phone number"
              keyboardType="phone-pad"
              leftIcon="call"
              error={errors.phone}
            />

            <Input
              label="Password"
              value={formData.password}
              onChangeText={(value) => updateFormData('password', value)}
              placeholder="Create a password"
              secureTextEntry
              leftIcon="lock-closed"
              error={errors.password}
            />

            <Input
              label="Confirm Password"
              value={formData.password_confirm}
              onChangeText={(value) => updateFormData('password_confirm', value)}
              placeholder="Confirm your password"
              secureTextEntry
              leftIcon="lock-closed"
              error={errors.password_confirm}
            />

            <Button
              title="Create Account"
              onPress={handleRegister}
              loading={registerMutation.isPending}
              style={styles.registerButton}
            />
          </View>

          <View style={styles.footer}>
            <Text variant="body" color="secondary" align="center">
              Already have an account?{' '}
              <Text variant="body" color="accent" onPress={handleSignIn}>
                Sign In
              </Text>
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  header: {
    marginTop: 40,
    marginBottom: 32,
  },
  title: {
    marginBottom: 8,
  },
  subtitle: {
    marginBottom: 0,
  },
  form: {
    flex: 1,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfInput: {
    flex: 1,
    marginRight: 8,
  },
  registerButton: {
    marginTop: 8,
    marginBottom: 32,
  },
  footer: {
    paddingBottom: 32,
  },
});

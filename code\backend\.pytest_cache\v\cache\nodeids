["authentication/test_acceptance.py::EmailVerificationAcceptanceTests::test_user_can_verify_email_with_valid_token", "authentication/test_acceptance.py::SocialAuthenticationAcceptanceTests::test_user_can_authenticate_with_google", "authentication/test_acceptance.py::UserLoginAcceptanceTests::test_account_lockout_after_failed_attempts", "authentication/test_acceptance.py::UserLoginAcceptanceTests::test_user_can_login_with_valid_credentials", "authentication/test_acceptance.py::UserLoginAcceptanceTests::test_user_cannot_login_with_invalid_credentials", "authentication/test_acceptance.py::UserRegistrationAcceptanceTests::test_user_can_register_with_email_and_password", "authentication/test_acceptance.py::UserRegistrationAcceptanceTests::test_user_cannot_register_with_duplicate_email", "authentication/test_acceptance.py::UserRegistrationAcceptanceTests::test_user_cannot_register_with_weak_password", "authentication/test_urls.py::AuthenticationURLAccessTests::test_login_url_accessible", "authentication/test_urls.py::AuthenticationURLAccessTests::test_profile_url_requires_authentication", "authentication/test_urls.py::AuthenticationURLAccessTests::test_register_url_accessible", "authentication/test_urls.py::AuthenticationURLTests::test_login_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_logout_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_password_reset_confirm_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_password_reset_request_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_profile_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_register_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_social_auth_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_token_refresh_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_verify_email_url_resolves", "authentication/tests.py::AuthenticationAPITests::test_account_lockout_after_failed_attempts", "authentication/tests.py::AuthenticationAPITests::test_protected_endpoint_requires_authentication", "authentication/tests.py::AuthenticationAPITests::test_protected_endpoint_with_valid_token", "authentication/tests.py::AuthenticationAPITests::test_token_refresh_invalid_token", "authentication/tests.py::AuthenticationAPITests::test_token_refresh_success", "authentication/tests.py::AuthenticationAPITests::test_user_login_invalid_credentials", "authentication/tests.py::AuthenticationAPITests::test_user_login_success", "authentication/tests.py::AuthenticationAPITests::test_user_login_unverified_account", "authentication/tests.py::AuthenticationAPITests::test_user_registration_duplicate_email", "authentication/tests.py::AuthenticationAPITests::test_user_registration_invalid_password", "authentication/tests.py::AuthenticationAPITests::test_user_registration_success", "authentication/tests.py::UserModelTests::test_account_lockout_mechanism", "authentication/tests.py::UserModelTests::test_create_superuser", "authentication/tests.py::UserModelTests::test_create_user_with_email", "authentication/tests.py::UserModelTests::test_create_user_without_email_raises_error", "authentication/tests.py::UserModelTests::test_phone_number_validation", "authentication/tests.py::UserModelTests::test_reset_failed_login_attempts", "authentication/tests.py::UserModelTests::test_user_full_name_property", "authentication/tests.py::UserModelTests::test_user_role_choices", "authentication/tests.py::UserModelTests::test_verify_email_method"]
"""
Authentication models for Vierla Beauty Services Marketplace
Custom User model with role-based functionality and security features
"""

from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.core.validators import RegexValidator
from django.db import models
from django.utils import timezone as django_timezone
from django.utils.translation import gettext_lazy as _
from datetime import timedelta
import uuid


class UserManager(BaseUserManager):
    """Custom user manager for email-based authentication"""

    def create_user(self, email, password=None, **extra_fields):
        """Create and return a regular user with an email and password"""
        if not email:
            raise ValueError(_('The Email field must be set'))

        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """Create and return a superuser with an email and password"""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('role', 'admin')
        extra_fields.setdefault('is_verified', True)
        extra_fields.setdefault('account_status', 'active')

        if extra_fields.get('is_staff') is not True:
            raise ValueError(_('Superuser must have is_staff=True.'))
        if extra_fields.get('is_superuser') is not True:
            raise ValueError(_('Superuser must have is_superuser=True.'))

        return self.create_user(email, password, **extra_fields)


class User(AbstractUser):
    """
    Custom User model supporting both customers and service providers
    Enhanced for mobile-first architecture with security features
    """

    class UserRole(models.TextChoices):
        CUSTOMER = 'customer', _('Customer')
        SERVICE_PROVIDER = 'service_provider', _('Service Provider')
        ADMIN = 'admin', _('Admin')

    class AccountStatus(models.TextChoices):
        ACTIVE = 'active', _('Active')
        INACTIVE = 'inactive', _('Inactive')
        SUSPENDED = 'suspended', _('Suspended')
        PENDING_VERIFICATION = 'pending_verification', _('Pending Verification')

    # Phone number validator
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message=_(
            "Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.")
    )

    # Basic Information
    email = models.EmailField(_('email address'), unique=True)
    phone = models.CharField(
        _('phone number'),
        validators=[phone_regex],
        max_length=17,
        blank=True,
        help_text=_('Phone number in international format')
    )
    role = models.CharField(
        _('user role'),
        max_length=20,
        choices=UserRole.choices,
        default=UserRole.CUSTOMER
    )

    # Profile Information
    avatar = models.ImageField(
        _('avatar'),
        upload_to='avatars/%Y/%m/',
        blank=True,
        null=True,
        help_text=_('Profile picture')
    )
    date_of_birth = models.DateField(
        _('date of birth'),
        blank=True,
        null=True
    )
    bio = models.TextField(
        _('biography'),
        max_length=500,
        blank=True,
        help_text=_('Brief description about yourself')
    )

    # Account Status and Security
    account_status = models.CharField(
        _('account status'),
        max_length=20,
        choices=AccountStatus.choices,
        default=AccountStatus.PENDING_VERIFICATION
    )
    is_verified = models.BooleanField(
        _('verified'),
        default=False,
        help_text=_(
            'Designates whether this user has verified their email address.')
    )
    email_verified_at = models.DateTimeField(
        _('email verified at'),
        blank=True,
        null=True
    )
    phone_verified_at = models.DateTimeField(
        _('phone verified at'),
        blank=True,
        null=True
    )

    # Mobile-specific fields
    device_token = models.TextField(
        _('device token'),
        blank=True,
        help_text=_('Push notification device token')
    )

    # Security fields
    failed_login_attempts = models.PositiveIntegerField(
        _('failed login attempts'),
        default=0
    )
    account_locked_until = models.DateTimeField(
        _('account locked until'),
        blank=True,
        null=True
    )
    last_activity = models.DateTimeField(
        _('last activity'),
        blank=True,
        null=True
    )
    last_login_ip = models.GenericIPAddressField(
        _('last login IP'),
        blank=True,
        null=True
    )

    # Timestamps
    created_at = models.DateTimeField(
        _('created at'),
        auto_now_add=True
    )
    updated_at = models.DateTimeField(
        _('updated at'),
        auto_now=True
    )

    # Use email as the unique identifier
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    # Custom manager
    objects = UserManager()

    class Meta:
        db_table = 'users'
        verbose_name = _('User')
        verbose_name_plural = _('Users')
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['role']),
            models.Index(fields=['account_status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['is_verified']),
        ]

    def __str__(self):
        return f"{self.get_full_name()} ({self.email})"

    def get_full_name(self):
        """Return the first_name plus the last_name, with a space in between."""
        full_name = f'{self.first_name} {self.last_name}'
        return full_name.strip()

    @property
    def full_name(self):
        """Property version of get_full_name for serializers"""
        return self.get_full_name()

    @property
    def is_customer(self):
        """Check if user is a customer"""
        return self.role == self.UserRole.CUSTOMER

    @property
    def is_service_provider(self):
        """Check if user is a service provider"""
        return self.role == self.UserRole.SERVICE_PROVIDER

    @property
    def is_account_locked(self):
        """Check if account is currently locked"""
        if self.account_locked_until:
            return django_timezone.now() < self.account_locked_until
        return False

    def lock_account(self, duration_minutes=30):
        """Lock account for specified duration"""
        self.account_locked_until = django_timezone.now() + timedelta(minutes=duration_minutes)
        self.save(update_fields=['account_locked_until'])

    def unlock_account(self):
        """Unlock account and reset failed login attempts"""
        self.account_locked_until = None
        self.failed_login_attempts = 0
        self.save(update_fields=['account_locked_until', 'failed_login_attempts'])

    def increment_failed_login(self):
        """Increment failed login attempts and lock if threshold reached"""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= 5:  # Lock after 5 failed attempts
            self.lock_account()
        self.save(update_fields=['failed_login_attempts'])

    def reset_failed_login(self):
        """Reset failed login attempts on successful login"""
        if self.failed_login_attempts > 0:
            self.failed_login_attempts = 0
            self.save(update_fields=['failed_login_attempts'])

    def update_last_activity(self):
        """Update last activity timestamp"""
        self.last_activity = django_timezone.now()
        self.save(update_fields=['last_activity'])

    def verify_email(self):
        """Mark email as verified"""
        self.is_verified = True
        self.email_verified_at = django_timezone.now()
        if self.account_status == self.AccountStatus.PENDING_VERIFICATION:
            self.account_status = self.AccountStatus.ACTIVE
        self.save(update_fields=['is_verified', 'email_verified_at', 'account_status'])

    def verify_phone(self):
        """Mark phone as verified"""
        self.phone_verified_at = django_timezone.now()
        self.save(update_fields=['phone_verified_at'])


class EmailVerificationToken(models.Model):
    """
    Email verification tokens for user registration
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='email_tokens'
    )
    token = models.CharField(
        _('verification token'),
        max_length=255,
        unique=True
    )
    created_at = models.DateTimeField(
        _('created at'),
        auto_now_add=True
    )
    expires_at = models.DateTimeField(
        _('expires at')
    )
    is_used = models.BooleanField(
        _('is used'),
        default=False
    )

    class Meta:
        db_table = 'email_verification_tokens'
        verbose_name = _('Email Verification Token')
        verbose_name_plural = _('Email Verification Tokens')
        indexes = [
            models.Index(fields=['token']),
            models.Index(fields=['expires_at']),
        ]

    def save(self, *args, **kwargs):
        """Override save to generate token and set expiration"""
        if not self.token:
            self.token = uuid.uuid4().hex
        if not self.expires_at:
            self.expires_at = django_timezone.now() + timedelta(hours=24)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Email verification token for {self.user.email}"

    @property
    def is_expired(self):
        """Check if token is expired"""
        return django_timezone.now() > self.expires_at

    @property
    def is_valid(self):
        """Check if token is valid (not used and not expired)"""
        return not self.is_used and not self.is_expired


class PasswordResetToken(models.Model):
    """
    Password reset tokens for secure password recovery
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='password_reset_tokens'
    )
    token = models.CharField(
        _('reset token'),
        max_length=255,
        unique=True
    )
    created_at = models.DateTimeField(
        _('created at'),
        auto_now_add=True
    )
    expires_at = models.DateTimeField(
        _('expires at')
    )
    is_used = models.BooleanField(
        _('is used'),
        default=False
    )

    class Meta:
        db_table = 'password_reset_tokens'
        verbose_name = _('Password Reset Token')
        verbose_name_plural = _('Password Reset Tokens')
        indexes = [
            models.Index(fields=['token']),
            models.Index(fields=['expires_at']),
        ]

    def save(self, *args, **kwargs):
        """Override save to generate token and set expiration"""
        if not self.token:
            self.token = uuid.uuid4().hex
        if not self.expires_at:
            self.expires_at = django_timezone.now() + timedelta(hours=1)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Password reset token for {self.user.email}"

    @property
    def is_expired(self):
        """Check if token is expired"""
        return django_timezone.now() > self.expires_at

    @property
    def is_valid(self):
        """Check if token is valid (not used and not expired)"""
        return not self.is_used and not self.is_expired
